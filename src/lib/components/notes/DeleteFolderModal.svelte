<script lang="ts">
	import { createEventDispatcher, getContext } from 'svelte';
	import { fade } from 'svelte/transition';
	import { flyAndScale } from '$lib/utils/transitions';
	import type { NoteFolderTreeNode } from '$lib/apis/note-folders';
	import type { I18n } from '$lib/models/i18n';

	const i18n = getContext<I18n>('i18n');
	const dispatch = createEventDispatcher();

	export let show = false;
	export let folder: NoteFolderTreeNode | null = null;

	let modalElement: HTMLElement | null = null;

	const handleKeyDown = (event: KeyboardEvent) => {
		if (event.key === 'Escape') {
			cancelHandler();
		}
		if (event.key === 'Enter') {
			confirmHandler();
		}
	};

	const confirmHandler = () => {
		dispatch('confirm', folder);
		show = false;
	};

	const cancelHandler = () => {
		show = false;
		dispatch('cancel');
	};

	$: if (show && modalElement) {
		document.body.appendChild(modalElement);
		window.addEventListener('keydown', handleKeyDown);
		document.body.style.overflow = 'hidden';
	} else if (modalElement) {
		window.removeEventListener('keydown', handleKeyDown);
		if (document.body.contains(modalElement)) {
			document.body.removeChild(modalElement);
		}
		document.body.style.overflow = 'unset';
	}
</script>

{#if show && folder}
	<!-- svelte-ignore a11y-click-events-have-key-events -->
	<!-- svelte-ignore a11y-no-static-element-interactions -->
	<div
		bind:this={modalElement}
		class="fixed top-0 right-0 left-0 bottom-0 bg-black/60 w-full h-screen max-h-[100dvh] flex justify-center z-99999999 overflow-hidden overscroll-contain"
		in:fade={{ duration: 10 }}
		on:mousedown={cancelHandler}
	>
		<div
			class="m-auto rounded-2xl max-w-full w-[32rem] mx-2 bg-white dark:bg-gray-900 max-h-[100dvh] shadow-3xl"
			in:flyAndScale
			on:mousedown={(e) => {
				e.stopPropagation();
			}}
		>
			<div class="px-[1.75rem] py-6 flex flex-col">
				<div class="text-lg font-semibold dark:text-white mb-2.5">
					{$i18n.t('Delete Folder')}
				</div>

				<div class="text-sm text-gray-600 dark:text-gray-300 flex-1 mb-6">
					{$i18n.t('Are you sure you want to delete the folder')} "<span class="font-semibold">{folder.name}</span>"?
					<br />
					{$i18n.t('This action cannot be undone.')}
				</div>

				<div class="flex justify-between gap-1.5">
					<button
						class="bg-gray-100 hover:bg-gray-200 text-gray-800 dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-white font-medium w-full py-2.5 rounded-lg transition"
						on:click={cancelHandler}
						type="button"
					>
						{$i18n.t('Cancel')}
					</button>
					<button
						class="bg-red-600 hover:bg-red-700 text-white font-medium w-full py-2.5 rounded-lg transition"
						on:click={confirmHandler}
						type="button"
					>
						{$i18n.t('Delete')}
					</button>
				</div>
			</div>
		</div>
	</div>
{/if}
