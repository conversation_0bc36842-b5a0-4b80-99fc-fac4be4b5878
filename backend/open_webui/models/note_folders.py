import time
import uuid
from typing import Optional, List
import logging

from open_webui.internal.db import Base, get_db
from open_webui.env import SRC_LOG_LEVELS
from pydantic import BaseModel, ConfigDict, validator
from sqlalchemy import (
    BigInteger,
    Column,
    String,
    Text,
    JSON,
    Integer,
    ForeignKey,
    Index,
)
from sqlalchemy.orm import relationship

log = logging.getLogger(__name__)
log.setLevel(SRC_LOG_LEVELS["MODELS"])

####################
# NoteFolder DB Schema
####################


class NoteFolder(Base):
    __tablename__ = "note_folder"

    id = Column(String, primary_key=True)
    user_id = Column(String, nullable=False, index=True)

    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    color = Column(String(7), nullable=True)  # 十六進制顏色代碼 #RRGGBB

    parent_id = Column(String, ForeignKey("note_folder.id"), nullable=True, index=True)
    sort_order = Column(Integer, default=0)

    access_control = Column(JSON, nullable=True)

    created_at = Column(BigInteger, nullable=False)
    updated_at = Column(BigInteger, nullable=False)

    # 添加索引以提高查詢性能
    __table_args__ = (
        Index("idx_note_folder_user_parent", "user_id", "parent_id"),
        Index("idx_note_folder_user_name", "user_id", "name"),
    )


class NoteFolderModel(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: str
    user_id: str

    name: str
    description: Optional[str] = None
    color: Optional[str] = None

    parent_id: Optional[str] = None
    sort_order: int = 0

    access_control: Optional[dict] = None

    created_at: int  # timestamp in epoch
    updated_at: int  # timestamp in epoch

    @validator("color")
    def validate_color(cls, v):
        if v is not None:
            if not v.startswith("#") or len(v) != 7:
                raise ValueError("Color must be in #RRGGBB format")
        return v

    @validator("name")
    def validate_name(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("Folder name cannot be empty")
        if len(v) > 255:
            raise ValueError("Folder name too long")
        return v.strip()


####################
# Forms
####################


class NoteFolderForm(BaseModel):
    name: str
    description: Optional[str] = None
    color: Optional[str] = None
    parent_id: Optional[str] = None
    sort_order: Optional[int] = 0
    access_control: Optional[dict] = None

    @validator("color")
    def validate_color(cls, v):
        if v is not None:
            if not v.startswith("#") or len(v) != 7:
                raise ValueError("Color must be in #RRGGBB format")
        return v

    @validator("name")
    def validate_name(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("Folder name cannot be empty")
        if len(v) > 255:
            raise ValueError("Folder name too long")
        return v.strip()


class NoteFolderUpdateForm(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    color: Optional[str] = None
    parent_id: Optional[str] = None
    sort_order: Optional[int] = None
    access_control: Optional[dict] = None

    @validator("color")
    def validate_color(cls, v):
        if v is not None:
            if not v.startswith("#") or len(v) != 7:
                raise ValueError("Color must be in #RRGGBB format")
        return v

    @validator("name")
    def validate_name(cls, v):
        if v is not None:
            if not v or len(v.strip()) == 0:
                raise ValueError("Folder name cannot be empty")
            if len(v) > 255:
                raise ValueError("Folder name too long")
            return v.strip()
        return v


class NoteFolderTreeNode(NoteFolderModel):
    """資料夾樹節點，包含子資料夾和筆記數量"""

    children: List["NoteFolderTreeNode"] = []
    note_count: int = 0
    total_note_count: int = 0  # 包含子資料夾的總筆記數


class NoteFolderMoveForm(BaseModel):
    target_parent_id: Optional[str] = None
    sort_order: Optional[int] = None


####################
# Database Operations
####################


class NoteFolderTable:
    def insert_new_folder(
        self,
        form_data: NoteFolderForm,
        user_id: str,
    ) -> Optional[NoteFolderModel]:
        with get_db() as db:
            # 檢查同一父資料夾下是否已存在同名資料夾
            existing = (
                db.query(NoteFolder)
                .filter(
                    NoteFolder.user_id == user_id,
                    NoteFolder.parent_id == form_data.parent_id,
                    NoteFolder.name == form_data.name,
                )
                .first()
            )

            if existing:
                raise ValueError(
                    "Folder with this name already exists in the same location"
                )

            # 檢查父資料夾是否存在且屬於當前用戶
            if form_data.parent_id:
                parent = (
                    db.query(NoteFolder)
                    .filter(
                        NoteFolder.id == form_data.parent_id,
                        NoteFolder.user_id == user_id,
                    )
                    .first()
                )
                if not parent:
                    raise ValueError("Parent folder not found")

            folder = NoteFolderModel(
                **{
                    "id": str(uuid.uuid4()),
                    "user_id": user_id,
                    **form_data.model_dump(),
                    "created_at": int(time.time()),
                    "updated_at": int(time.time()),
                }
            )

            new_folder = NoteFolder(**folder.model_dump())
            db.add(new_folder)
            db.commit()
            db.refresh(new_folder)
            return NoteFolderModel.model_validate(new_folder)

    def get_folders_by_user_id(self, user_id: str) -> List[NoteFolderModel]:
        with get_db() as db:
            folders = (
                db.query(NoteFolder)
                .filter(NoteFolder.user_id == user_id)
                .order_by(NoteFolder.sort_order, NoteFolder.name)
                .all()
            )
            return [NoteFolderModel.model_validate(folder) for folder in folders]

    def get_folder_by_id(
        self, folder_id: str, user_id: str
    ) -> Optional[NoteFolderModel]:
        with get_db() as db:
            folder = (
                db.query(NoteFolder)
                .filter(NoteFolder.id == folder_id, NoteFolder.user_id == user_id)
                .first()
            )
            return NoteFolderModel.model_validate(folder) if folder else None

    def update_folder_by_id(
        self,
        folder_id: str,
        form_data: NoteFolderUpdateForm,
        user_id: str,
    ) -> Optional[NoteFolderModel]:
        with get_db() as db:
            folder = (
                db.query(NoteFolder)
                .filter(NoteFolder.id == folder_id, NoteFolder.user_id == user_id)
                .first()
            )

            if not folder:
                return None

            # 檢查名稱衝突
            if form_data.name and form_data.name != folder.name:
                existing = (
                    db.query(NoteFolder)
                    .filter(
                        NoteFolder.user_id == user_id,
                        NoteFolder.parent_id == folder.parent_id,
                        NoteFolder.name == form_data.name,
                        NoteFolder.id != folder_id,
                    )
                    .first()
                )

                if existing:
                    raise ValueError(
                        "Folder with this name already exists in the same location"
                    )

            # 檢查循環引用
            if form_data.parent_id and form_data.parent_id != folder.parent_id:
                if self._would_create_cycle(
                    db, folder_id, form_data.parent_id, user_id
                ):
                    raise ValueError("Cannot move folder: would create a cycle")

            update_data = form_data.model_dump(exclude_unset=True)
            update_data["updated_at"] = int(time.time())

            for key, value in update_data.items():
                setattr(folder, key, value)

            db.commit()
            db.refresh(folder)
            return NoteFolderModel.model_validate(folder)

    def delete_folder_by_id(self, folder_id: str, user_id: str) -> bool:
        with get_db() as db:
            folder = (
                db.query(NoteFolder)
                .filter(NoteFolder.id == folder_id, NoteFolder.user_id == user_id)
                .first()
            )

            if not folder:
                return False

            # 檢查是否有子資料夾
            children = (
                db.query(NoteFolder)
                .filter(
                    NoteFolder.parent_id == folder_id, NoteFolder.user_id == user_id
                )
                .count()
            )

            if children > 0:
                raise ValueError("Cannot delete folder: contains subfolders")

            # 檢查是否有筆記
            from open_webui.models.notes import Note

            notes = (
                db.query(Note)
                .filter(Note.folder_id == folder_id, Note.user_id == user_id)
                .count()
            )

            if notes > 0:
                raise ValueError("Cannot delete folder: contains notes")

            db.delete(folder)
            db.commit()
            return True

    def _would_create_cycle(
        self, db, folder_id: str, new_parent_id: str, user_id: str
    ) -> bool:
        """檢查移動資料夾是否會創建循環引用"""
        current_id = new_parent_id
        visited = set()

        while current_id:
            if current_id == folder_id:
                return True
            if current_id in visited:
                break
            visited.add(current_id)

            parent = (
                db.query(NoteFolder)
                .filter(NoteFolder.id == current_id, NoteFolder.user_id == user_id)
                .first()
            )
            current_id = parent.parent_id if parent else None

        return False

    def get_folder_tree(self, user_id: str) -> List[NoteFolderTreeNode]:
        """獲取用戶的資料夾樹狀結構"""
        with get_db() as db:
            # 獲取所有資料夾
            folders = (
                db.query(NoteFolder)
                .filter(NoteFolder.user_id == user_id)
                .order_by(NoteFolder.sort_order, NoteFolder.name)
                .all()
            )

            # 獲取每個資料夾的筆記數量
            from open_webui.models.notes import Note

            note_counts = {}
            for folder in folders:
                count = (
                    db.query(Note)
                    .filter(Note.folder_id == folder.id, Note.user_id == user_id)
                    .count()
                )
                note_counts[folder.id] = count

            # 構建樹狀結構
            folder_map = {}
            root_folders = []

            # 創建節點映射
            for folder in folders:
                node = NoteFolderTreeNode(
                    **folder.__dict__,
                    children=[],
                    note_count=note_counts.get(folder.id, 0),
                    total_note_count=note_counts.get(folder.id, 0),
                )
                folder_map[folder.id] = node

                if folder.parent_id is None:
                    root_folders.append(node)

            # 建立父子關係
            for folder in folders:
                if folder.parent_id and folder.parent_id in folder_map:
                    parent = folder_map[folder.parent_id]
                    child = folder_map[folder.id]
                    parent.children.append(child)

            # 計算總筆記數（包含子資料夾）
            def calculate_total_notes(node: NoteFolderTreeNode) -> int:
                total = node.note_count
                for child in node.children:
                    total += calculate_total_notes(child)
                node.total_note_count = total
                return total

            for root in root_folders:
                calculate_total_notes(root)

            return root_folders

    def get_folder_path(self, folder_id: str, user_id: str) -> List[NoteFolderModel]:
        """獲取資料夾的完整路徑"""
        with get_db() as db:
            path = []
            current_id = folder_id

            while current_id:
                folder = (
                    db.query(NoteFolder)
                    .filter(NoteFolder.id == current_id, NoteFolder.user_id == user_id)
                    .first()
                )

                if not folder:
                    break

                path.insert(0, NoteFolderModel.model_validate(folder))
                current_id = folder.parent_id

            return path

    def move_folder(
        self,
        folder_id: str,
        form_data: NoteFolderMoveForm,
        user_id: str,
    ) -> Optional[NoteFolderModel]:
        """移動資料夾到新位置"""
        with get_db() as db:
            folder = (
                db.query(NoteFolder)
                .filter(NoteFolder.id == folder_id, NoteFolder.user_id == user_id)
                .first()
            )

            if not folder:
                return None

            # 檢查目標父資料夾是否存在
            if form_data.target_parent_id:
                target_parent = (
                    db.query(NoteFolder)
                    .filter(
                        NoteFolder.id == form_data.target_parent_id,
                        NoteFolder.user_id == user_id,
                    )
                    .first()
                )
                if not target_parent:
                    raise ValueError("Target parent folder not found")

                # 檢查循環引用
                if self._would_create_cycle(
                    db, folder_id, form_data.target_parent_id, user_id
                ):
                    raise ValueError("Cannot move folder: would create a cycle")

            # 更新資料夾位置
            folder.parent_id = form_data.target_parent_id
            if form_data.sort_order is not None:
                folder.sort_order = form_data.sort_order
            folder.updated_at = int(time.time())

            db.commit()
            db.refresh(folder)
            return NoteFolderModel.model_validate(folder)

    def get_subfolders(
        self, parent_id: Optional[str], user_id: str
    ) -> List[NoteFolderModel]:
        """獲取指定父資料夾下的子資料夾"""
        with get_db() as db:
            folders = (
                db.query(NoteFolder)
                .filter(
                    NoteFolder.parent_id == parent_id, NoteFolder.user_id == user_id
                )
                .order_by(NoteFolder.sort_order, NoteFolder.name)
                .all()
            )

            return [NoteFolderModel.model_validate(folder) for folder in folders]

    def get_shared_folders_by_user_id(self, user_id: str) -> List[NoteFolderModel]:
        """獲取與用戶共用的資料夾（不包括用戶擁有的資料夾）"""
        from open_webui.utils.access_control import has_access

        with get_db() as db:
            # 獲取所有不是用戶擁有的資料夾
            all_folders = (
                db.query(NoteFolder).filter(NoteFolder.user_id != user_id).all()
            )

            shared_folders = []
            for folder in all_folders:
                # 檢查用戶是否有讀取權限
                if folder.access_control is None or has_access(  # 公開資料夾
                    user_id, "read", folder.access_control
                ):
                    shared_folders.append(NoteFolderModel.model_validate(folder))

            # 按名稱排序
            shared_folders.sort(key=lambda x: x.name)
            return shared_folders

    def get_shared_folder_tree(self, user_id: str) -> List[NoteFolderTreeNode]:
        """獲取用戶的共用資料夾樹狀結構"""
        from open_webui.utils.access_control import has_access

        with get_db() as db:
            # 獲取所有不是用戶擁有的資料夾
            all_folders = (
                db.query(NoteFolder)
                .filter(NoteFolder.user_id != user_id)
                .order_by(NoteFolder.sort_order, NoteFolder.name)
                .all()
            )

            # 過濾出有權限的資料夾
            accessible_folders = []
            for folder in all_folders:
                if folder.access_control is None or has_access(  # 公開資料夾
                    user_id, "read", folder.access_control
                ):
                    accessible_folders.append(folder)

            # 獲取每個資料夾的筆記數量（只計算用戶有權限的筆記）
            from open_webui.models.notes import Note

            note_counts = {}
            for folder in accessible_folders:
                # 獲取資料夾中用戶有權限的筆記
                notes = db.query(Note).filter(Note.folder_id == folder.id).all()
                count = 0
                for note in notes:
                    if (
                        note.user_id == user_id  # 用戶擁有的筆記
                        or note.access_control is None  # 公開筆記
                        or has_access(user_id, "read", note.access_control)
                    ):  # 有權限的筆記
                        count += 1
                note_counts[folder.id] = count

            # 構建樹狀結構
            folder_map = {}
            root_folders = []

            # 創建節點映射
            for folder in accessible_folders:
                node = NoteFolderTreeNode(
                    **folder.__dict__,
                    children=[],
                    note_count=note_counts.get(folder.id, 0),
                    total_note_count=note_counts.get(folder.id, 0),
                )
                folder_map[folder.id] = node

                # 只有父資料夾也在可存取列表中，才建立父子關係
                if folder.parent_id is None or folder.parent_id not in [
                    f.id for f in accessible_folders
                ]:
                    root_folders.append(node)

            # 建立父子關係（只在可存取的資料夾之間）
            for folder in accessible_folders:
                if (
                    folder.parent_id
                    and folder.parent_id in folder_map
                    and folder.id in folder_map
                ):
                    parent = folder_map[folder.parent_id]
                    child = folder_map[folder.id]
                    parent.children.append(child)
                    # 如果子資料夾已經在根目錄列表中，需要移除
                    if child in root_folders:
                        root_folders.remove(child)

            # 計算總筆記數（包含子資料夾）
            def calculate_total_notes(node: NoteFolderTreeNode) -> int:
                total = node.note_count
                for child in node.children:
                    total += calculate_total_notes(child)
                node.total_note_count = total
                return total

            for root in root_folders:
                calculate_total_notes(root)

            return root_folders


# 創建表實例
NoteFolders = NoteFolderTable()
